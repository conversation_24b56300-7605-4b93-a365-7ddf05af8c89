# 数据集划分说明

本目录包含按8:1:1比例划分的HC和MCI组功能和结构脑网络数据。

## 目录结构

```
dataset_splits/
├── train/          # 训练集 (80%)
│   ├── edge_lists/
│   │   ├── HC/     # HC组边列表文件
│   │   └── MCI/    # MCI组边列表文件
│   └── node_features/
│       ├── HC/     # HC组节点特征文件
│       └── MCI/    # MCI组节点特征文件
├── val/            # 验证集 (10%)
│   ├── edge_lists/
│   │   ├── HC/
│   │   └── MCI/
│   └── node_features/
│       ├── HC/
│       └── MCI/
└── test/           # 测试集 (10%)
    ├── edge_lists/
    │   ├── HC/
    │   └── MCI/
    └── node_features/
        ├── HC/
        └── MCI/
```

## 数据统计

### HC组 (健康对照组)
- **总被试数**: 283
- **训练集**: 226 个被试 (79.9%)
- **验证集**: 28 个被试 (9.9%)
- **测试集**: 29 个被试 (10.2%)

### MCI组 (轻度认知障碍组)
- **总被试数**: 213
- **训练集**: 170 个被试 (79.8%)
- **验证集**: 21 个被试 (9.9%)
- **测试集**: 22 个被试 (10.3%)

## 文件类型说明

### 边列表文件 (edge_lists)
- **格式**: CSV文件
- **命名**: `{被试ID}_edges.csv`
- **内容**: 脑网络的边连接信息，包含source、target、weight三列
- **NPZ文件**: `{被试ID}_gcn_data.npz` - 预处理的GCN数据

### 节点特征文件 (node_features)
- **格式**: TXT和MAT文件
- **命名**: `ROISignals_{被试ID}.txt` 和 `ROISignals_{被试ID}.mat`
- **内容**: 116个脑区的节点特征数据

## 数据完整性验证

✅ **无重叠被试**: 训练集、验证集、测试集之间无被试重叠
✅ **数据完整性**: 每个被试都同时包含边列表和节点特征文件
✅ **比例准确**: 实际划分比例与期望的8:1:1比例一致

## 使用说明

1. **训练阶段**: 使用 `train/` 目录下的数据进行模型训练
2. **验证阶段**: 使用 `val/` 目录下的数据进行超参数调优和模型选择
3. **测试阶段**: 使用 `test/` 目录下的数据进行最终性能评估

## 注意事项

- 数据划分使用固定随机种子(42)，确保结果可重现
- 划分是在被试层面进行的，保证同一被试的所有文件都在同一个集合中
- 每个被试包含功能连接(边列表)和结构特征(节点特征)两种模态的数据
- 建议在模型训练过程中不要使用测试集数据，以避免数据泄露

## 生成脚本

- `split_dataset.py`: 数据集划分脚本
- `verify_split.py`: 数据完整性验证脚本

生成时间: 2025年1月
